# Backend Application Plan: Budejovice.info

## 1. Functional Requirements

### 1.1. Overview
The backend will be responsible for collecting data from various external sources, processing it, caching it, and exposing it through a clean REST API for the frontend application. It will consist of two main components: a scheduler for running data-fetching jobs and a web server for the API.

### 1.2. System Architecture
- **Language:** Python
- **Modules:**
    1.  **API Server:** A web application (e.g., using FastAPI or Flask) to serve the REST API.
    2.  **Job Scheduler:** A separate process running cron-like jobs (e.g., using `apscheduler` or a similar library) to periodically fetch and update data.
- **Data Storage:**
    - **Primary:** PostgreSQL. The system should be designed to work with a standard PostgreSQL connection string, making it compatible with both a local Docker instance and a managed service like Supabase.
    - **Cache:** Redis. All data served via the API will be retrieved from the Redis cache to ensure fast response times and reduce load on the external data sources and the database.
- **Deployment:** The entire backend (API server, job scheduler, Redis, and optional PostgreSQL) will be containerized and managed via a `docker-compose.yml` file.

### 1.3. API Specification

The API will provide data to the frontend. It must include an OpenAPI (Swagger) specification, which should be automatically generated (a feature of FastAPI).

- **Base URL:** `/api/v1`
- **Endpoints:**
    - **`GET /parking`**: Returns cached data for parking availability.
    - **`GET /offices`**: Returns a static list of the office branches.
    - **`GET /offices/{office_id}/queues`**: Returns cached queue data for the specified office (`radnice`, `jeronymova`, `knezska`).
    - **`GET /swimming_pool`**: Returns cached data for the swimming pool capacity.
- **Data Flow:** When an API endpoint is called, the server will attempt to fetch the corresponding data from the Redis cache. If the data is not in the cache (or is expired), it should return the last known data from the database while the next job run populates the cache again. The jobs are the only process writing to the DB and cache.

### 1.4. Job Scheduler Module

This module will run a set of jobs to periodically scrape data from external sources.

#### 1.4.1. General Job Design
- **Genericity:** A generic job runner should be implemented. Each specific scraping task will be a "job" that can be configured and scheduled independently.
- **Configuration:** All job parameters (URLs, cron schedules, request parameters) must be configurable, for example, through environment variables or a configuration file.
- **Error Handling:** Jobs must be resilient. If a source is unavailable or returns invalid data, the job should log the error and retry later without crashing the scheduler. The last valid data should be kept in the database.
- **Data Processing:** After fetching, each job will parse the raw data (HTML or JSON), transform it into the standardized database schema, and store it in the PostgreSQL database. After storing in the DB, it will update the Redis cache with the fresh data.

#### 1.4.2. Specific Jobs

1.  **Parking Data Job**
    - **Source:** `https://dopravniinfo.c-budejovice.cz/TabularData/Parking?order=undefined&dir=undefined`
    - **Method:** `GET`
    - **Schedule:** Every 5 minutes.
    - **Parsing Logic:**
        - Fetch the HTML content.
        - Use an HTML parsing library (e.g., BeautifulSoup) to find the `table.output-table`.
        - Iterate through each `<tr>` in the table body.
        - For each row, extract the text from the `<td>` elements corresponding to: Name, Location, Capacity, Available Spaces, Occupancy, and Time.
        - Handle rows where data is not available (e.g., "Aktuální údaje o obsazenosti nejsou k dispozici.").

2.  **Office Queues Job (x3)**
    - **Generality:** A single job function should handle all three offices, parameterized by `poboID` and URL.
    - **Schedule:** Every 1 minute for each office.
    - **Sources & Parameters:**
        - **Kněžská:**
            - **URL:** `https://rezervace-knezska.c-budejovice.cz/json.php?method=mon_dej_cinnost`
            - **Method:** `POST`
            - **Body:** `{"poboID":"1071"}`
        - **Radnice:**
            - **URL:** `https://rezervace-radnice.c-budejovice.cz/json.php?method=mon_dej_cinnost`
            - **Method:** `POST`
            - **Body:** `{"poboID":"1070"}`
        - **Jeronýmova:**
            - **URL:** `https://rezervace-jeronymova.c-budejovice.cz/json.php?method=mon_dej_cinnost`
            - **Method:** `POST`
            - **Body:** `{"poboID":"1069"}`
    - **Parsing Logic:**
        - The response is JSON. Parse the JSON string.
        - The relevant data is in the `return` array.
        - Iterate through the array. Filter out inactive services (`m_aktivni: 0`).
        - Map the JSON fields (`nazev`, `fronta`, `doba_cekani_odhad`, `pocet_prepazek`) to the database schema.

3.  **Swimming Pool Job**
    - **Source:** `https://www.szcb.cz/plavecky-stadion`
    - **Method:** `GET`
    - **Schedule:** Every 5 minutes.
    - **Parsing Logic:**
        - Fetch the HTML content.
        - Use an HTML parsing library to find the `div.item-panel`.
        - Extract the text content from the following child divs:
            - `div.panel-percentage`: Occupancy percentage (e.g., "18 %").
            - `div.panel-ratio`: Occupancy ratio (e.g., "60 / 340").
        - Parse these strings to get the numerical values.

## 2. Non-Functional Requirements

### 2.1. Database Schema (High-Level)

- **`parking_lots` table:**
    - `id` (PK)
    - `name` (text, unique)
    - `location` (text)
    - `capacity` (integer)
    - `available` (integer)
    - `occupancy_percent` (integer)
    - `last_updated_source` (text)
    - `last_updated_db` (timestamp)

- **`office_branches` table:**
    - `id` (PK)
    - `internal_id` (text, e.g., "radnice", "knezska")
    - `name` (text)
    - `address` (text)

- **`office_queues` table:**
    - `id` (PK)
    - `branch_id` (FK to `office_branches`)
    - `service_name` (text)
    - `waiting_count` (integer)
    - `estimated_wait_minutes` (integer)
    - `open_counters` (integer)
    - `last_updated_db` (timestamp)

- **`swimming_pool_stats` table:**
    - `id` (PK, only one row needed)
    - `name` (text)
    - `occupancy_percent` (integer)
    - `current_occupants` (integer)
    - `max_capacity` (integer)
    - `last_updated_db` (timestamp)

### 2.2. Caching
- **Strategy:** Write-through caching. The jobs will write to the database and then immediately update the Redis cache.
- **Keys:** Use clear and consistent Redis keys (e.g., `parking:latest`, `queues:radnice`, `swimming_pool:latest`).
- **Expiration:** The API will serve directly from Redis. The cache effectively "expires" whenever a job successfully runs and overwrites the existing keys with new data. A time-to-live (TTL) can also be set on the keys (e.g., 10 minutes) as a fallback in case a job fails, to prevent serving very stale data. This TTL should be configurable.

### 2.3. Configuration
- Use environment variables for all configurable parameters, including:
    - Database connection URL (`DATABASE_URL`)
    - Redis connection URL (`REDIS_URL`)
    - Job cron schedules (e.g., `PARKING_JOB_CRON`)
    - External data source URLs and parameters.
    - Cache TTLs.

### 2.4. Development & Deployment
- **Tooling:** The project will be developed using **Claude Code**.
- **`docker-compose.yml`:** A `docker-compose.yml` file must be created to orchestrate the services:
    - `api` service (running the Python web server)
    - `scheduler` service (running the Python job scheduler)
    - `redis` service (from the official Redis image)
    - `db` service (optional, from the official Postgres image, for local development)
- **Production:** The setup must be ready for production deployment where the `db` service is replaced by a connection to Supabase.
