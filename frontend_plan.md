# Frontend Application Plan: Budejovice.info

## 1. Functional Requirements

### 1.1. Overview
The frontend will be a single-page application (SPA) displaying real-time information about services in České Budějovice. It will be built using Lovable, which will handle the UI/UX design and component creation. The application will not require any user authentication.

### 1.2. Pages & Features

The application will consist of a main dashboard view with three primary sections or cards, each corresponding to a page's content. Clicking on a section could navigate to a more detailed view if necessary, but the primary goal is to present all key information on a single screen for quick access.

#### 1.2.1. Parking Availability
- **Display:** Show a list of major parking lots in České Budějovice.
- **Data Points for each lot:**
    - Name of the parking lot (`Nazev`)
    - Location/Street (`Misto`)
    - Total Capacity (`Kapacita`)
    - Currently Available Spaces (`VolnychMist`)
    - Occupancy Percentage (`Obsazenost`)
    - Last update time (`Cas`)
- **Interaction:** The list should be clearly readable. Lots with no available data should be indicated as such.

#### 1.2.2. Municipal Office Queues
- **Display:** Show the status of queues for different services at three different municipal office branches.
- **Branch Selection:** The user must be able to easily switch between the three branches:
    1.  Matriční úřad, ohlašovna pobytu (nám. Přemysla Otakara II.)
    2.  Oddělení EO, OP, CD a ŘP (Jeronýmova 1)
    3.  Oddělení evidence motorových vozidel (Kněžská 19)
- **Data Points for each service queue:**
    - Service Name (`nazev`)
    - Number of people waiting in the queue (`fronta`)
    - Estimated waiting time (`doba_cekani_odhad`) - display in minutes.
    - Number of open counters (`pocet_prepazek`)
- **Interaction:** A dropdown or tabbed interface will allow users to select the desired office branch. Services that are inactive (`m_aktivni: 0`) should be hidden.

#### 1.2.3. Swimming Pool Capacity
- **Display:** Show the current occupancy of the "Plavecký stadion".
- **Data Points:**
    - Occupancy percentage.
    - A ratio of current occupants to total capacity (e.g., "60 / 340").
    - A visual indicator (like a progress bar or gauge) to represent the occupancy level.

### 1.3. API Endpoints (to be provided by the backend)

The frontend will consume a REST API. The following endpoints are expected:

- **`GET /api/v1/parking`**
  - **Description:** Retrieves the current status of all monitored parking lots.
  - **Expected Response Body:**
    ```json
    {
      "last_updated": "2025-09-18T22:44:00Z",
      "parking_lots": [
        {
          "name": "Parkoviště Senovážné nám.",
          "location": "Senovážné nám.",
          "capacity": 94,
          "available": 75,
          "occupancy_percent": 20,
          "updated_at": "dnes 22:44"
        }
      ]
    }
    ```

- **`GET /api/v1/offices`**
  - **Description:** Retrieves a list of all available municipal office branches.
  - **Expected Response Body:**
    ```json
    [
        { "id": "radnice", "name": "Matriční úřad, ohlašovna pobytu", "address": "nám. Přemysla Otakara II. 1/1" },
        { "id": "jeronymova", "name": "Oddělení EO, OP, CD a ŘP", "address": "Jeronýmova 1" },
        { "id": "knezska", "name": "Oddělení evidence motorových vozidel", "address": "Kněžská 19" }
    ]
    ```

- **`GET /api/v1/offices/{office_id}/queues`**
  - **Description:** Retrieves the queue status for a specific office branch.
  - **`office_id` can be:** `radnice`, `jeronymova`, `knezska`.
  - **Expected Response Body:**
    ```json
    {
      "office_name": "Matriční úřad, ohlašovna pobytu",
      "last_updated": "2025-09-18T22:59:00Z",
      "queues": [
        {
          "service_name": "FO-Pokladna",
          "waiting": 0,
          "estimated_wait_minutes": 0,
          "open_counters": 6
        }
      ]
    }
    ```

- **`GET /api/v1/swimming_pool`**
  - **Description:** Retrieves the current capacity of the swimming pool.
  - **Expected Response Body:**
    ```json
    {
      "last_updated": "2025-09-18T23:00:00Z",
      "name": "Plavecký stadion",
      "occupancy_percent": 18,
      "current_occupants": 60,
      "max_capacity": 340
    }
    ```

### 1.4. Mock Data
For development and testing with Lovable, static mock data files should be created to simulate the API responses. These files should be stored in the project (e.g., in a `/mocks` directory) and should mirror the JSON structures defined above. This will allow for independent frontend development before the backend is complete.

## 2. Non-Functional Requirements

### 2.1. Design and UX
- **Tooling:** The UI/UX will be generated by **Lovable**. The prompts given to Lovable should emphasize a clean, modern, and intuitive design.
- **Responsiveness:** The application must be fully responsive and provide an excellent user experience on all devices, including desktops, tablets, and mobile phones.
- **Performance:** The application should load quickly and feel responsive. Data should be fetched asynchronously, and loading states (e.g., spinners) should be displayed while data is being retrieved.
- **Accessibility:** The application should adhere to modern accessibility standards (WCAG 2.1).

### 2.2. Technology Stack
- **Framework:** To be determined by Lovable, but likely React or a similar modern JavaScript framework.
- **Styling:** CSS-in-JS or a utility-first CSS framework like Tailwind CSS is recommended for easy styling and maintenance.

### 2.3. Deployment
- **Domain:** The final application will be deployed and accessible at `budejovice.info`.
- **Environment:** The frontend will be a static build that can be served from any static hosting provider or a simple web server container. It will be part of a larger Docker Compose setup.
