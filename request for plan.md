## Introduction
Need to create detailed definition of task to create a web application, i.e. detailed plan for frontend and for detailed plan for backend.
Expected output: two files - plans should contains functional and non functional requirements mentioned here, you can extend it and fix it.
Each file will contains full information to create frontend or backend. 
Do not generate any code, any code snippet, it is all about functional and non functional requirements, language agnostic. 

## App description:
Frontend should  display 3 pages:
  * one info about free parking slots, 
  * one for monitoring Přepážky na úřadě (inside page, user can select specific branch) 
  * one for Plavecký stadion wihh information about current capacity of pool 
The final domain where will be running will be budejovice.info
The whole app should be deployable using docker compose, including redis, database inside docker will optional, on production, Supabase will be used.



## Frontend app requirements:
* will be created using Lovable, so functional requirements should be defined in terms of using this AI tool.
* No login, no auth required
* frontend will be calling backend rest api
* plan should contain expected API provided by backend 
* Frontend will call API to get data, but for tests, we need to be able to use some mock data, especially in a case of using Lovable
full
* fully responsive modern design, newest technology stack depending on Lovelance, mobile compatible
* User should be able to fast display state of free parking slots, state of counter at the office (3x) and capacity of swimming pool.

## Backend app requirements:
* will be created using Claude Code and will be running inside docker containers, prefer docker compose for initial test startup.
* data will be stored in posgresql DB, I prefer using docker posgresql for start, but should be ready for supabase.
* redis will be used to spare loading still the same data from DB when clients wants to display data.
* bellow are some source of data, need to create generic job implementations to be able to create other jobs in the future
* backend will serve openapi spec and api, which will be consumed by frontend
* backend will consist of two modules, one for cronjobs and one for serving api to clients
* python will be used for both modules
* when called API from FE, it will use data from redis cache, expiration of cache will be configurable


### Details for jobs module
* all jobs will be configurable i.e. URLS, cron timing
* will periodically download data for parking slots for  from https://dopravniinfo.c-budejovice.cz/TabularData/Parking?order=undefined&dir=undefined Method GET
* will periodically download data for Oddělení evidence motorových vozidel
Kněžská 19, 1. nadzemní podlaží, České Budějovice, POST form data {"poboID":"1071","acLogin":null,"acHash":null} to https://rezervace-knezska.c-budejovice.cz/json.php?method=mon_dej_cinnost
* will periodically download data for Matriční úřad, ohlašovna pobytu
nám. Přemysla Otakara II. 1/1, 370 92 České Budějovice, POST form data {"poboID":"1070","acLogin":null,"acHash":null} to https://rezervace-radnice.c-budejovice.cz/json.php?method=mon_dej_cinnost
* will periodically download data for Oddělení EO, OP, CD a ŘP
Jeronýmova 1, České Budějovice (1. a 2. nadzemní podlaží) České Budějovice, POST form data {"poboID":"1069","acLogin":null,"acHash":null} to https://rezervace-jeronymova.c-budejovice.cz/json.php?method=mon_dej_cinnost
* will periodically download data from Plavecký stadion, e.g. Obsazenost 18 %, 60 / 340, GET https://www.szcb.cz/plavecky-stadion, these informations should be parsed from 



Attachments:
## Example of data from https://dopravniinfo.c-budejovice.cz/TabularData/Parking?order=undefined&dir=undefined

    <h1 class="output-table-caption"><span data-lang="LegendLayers.Layer.show_parking">Parkovací systémy</span></h1>
    <table class="output-table" style="display:none">
        <tr>
            <th><span data-order-fill>&nbsp;</span><span data-order="Nazev"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.Name">Název<br />lokality</span></th>
            <th><span data-order-fill>&nbsp;</span><span data-order="Misto"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.Place">Místo<br />lokality</span></th>
            <th><span data-order-fill>&nbsp;</span><span data-order="Kapacita"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.Capacity">Kapacita<br />lokality</span></th>
            <th><span data-order-fill>&nbsp;</span><span data-order="VolnychMist"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.AvailablePlaces">Volných<br />míst</span></th>
            <th><span data-order-fill>&nbsp;</span><span data-order="ObsazenoMist"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.OccupiedPlaces">Obsazeno<br />míst</span></th>
            <th><span data-order-fill>&nbsp;</span><span data-order="Obsazenost"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.Occupancy">Obsazenost</span></th>
            <th><span data-order-fill>&nbsp;</span><span data-order="Cas"><span data-desc>&dtrif;</span><span data-asc>&utrif;</span></span><span data-lang="LegendLayers.Layer.show_parking.DeviceTime">Čas</span></th>
        </tr>
            <tr>
                <td>Parkoviště Senov&#225;žn&#233; n&#225;m.</td>
                <td>Senov&#225;žn&#233; n&#225;m.</td>
                    <td style="width:10rem" class="">94</td>
                    <td style="width:10rem" class="">75</td>
                    <td style="width:10rem" class="">19</td>
                    <td style="width:10rem" class="">20 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>Parkoviště Mari&#225;nsk&#233; n&#225;m.</td>
                <td>Mari&#225;nsk&#233; n&#225;m.</td>
                    <td style="width:10rem" class="">184</td>
                    <td style="width:10rem" class="">120</td>
                    <td style="width:10rem" class="">64</td>
                    <td style="width:10rem" class="">35 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>Parkoviště J&#237;rovcova</td>
                <td>J&#237;rovcova</td>
                    <td style="width:10rem" class="">438</td>
                    <td style="width:10rem" class="">198</td>
                    <td style="width:10rem" class="">240</td>
                    <td style="width:10rem" class="">55 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>Parkovac&#237; dům Stromovka</td>
                <td>Stromovka</td>
                    <td style="width:10rem" class="">484</td>
                    <td style="width:10rem" class="">430</td>
                    <td style="width:10rem" class="">54</td>
                    <td style="width:10rem" class="">11 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>P2 IGY centrum</td>
                <td>Fr&#225;ni Šr&#225;mka</td>
                    <td colspan="5"><span style="display:block; text-align:center;" data-lang="Parking.None">Aktuální údaje o obsazenosti nejsou k dispozici.</span></td>
            </tr>
            <tr>
                <td>P1 IGY centrum</td>
                <td>Pek&#225;rensk&#225;</td>
                    <td colspan="5"><span style="display:block; text-align:center;" data-lang="Parking.None">Aktuální údaje o obsazenosti nejsou k dispozici.</span></td>
            </tr>
            <tr>
                <td>Mercury</td>
                <td>Dvoř&#225;kova</td>
                    <td style="width:10rem" class="">691</td>
                    <td style="width:10rem" class="">622</td>
                    <td style="width:10rem" class="">69</td>
                    <td style="width:10rem" class="">10 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>Dynamo</td>
                <td>Boženy Němcov&#233;</td>
                    <td style="width:10rem" class="">148</td>
                    <td style="width:10rem" class="">2</td>
                    <td style="width:10rem" class="">146</td>
                    <td style="width:10rem" class="">99 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>Dlouh&#225; louka</td>
                <td>Dlouh&#225; louka</td>
                    <td style="width:10rem" class="">204</td>
                    <td style="width:10rem" class="">19</td>
                    <td style="width:10rem" class="">185</td>
                    <td style="width:10rem" class="">91 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
            <tr>
                <td>City green park</td>
                <td>Goethova</td>
                    <td style="width:10rem" class="">316</td>
                    <td style="width:10rem" class="">80</td>
                    <td style="width:10rem" class="">236</td>
                    <td style="width:10rem" class="">75 %</td>
                    <td style="width:10rem" class=""><span data-lang='Date.Today'>dnes</span> 22:44</td>
            </tr>
    </table>
    <style>
        .output-table th {
            cursor: pointer;
            background-color: #eee;
        }

        .output-table [data-order-fill],
        .output-table [data-order] {
            display: inline-block;
            vertical-align: top;
            float: right;
            padding-left: 10px;
            color: red;
            font-size: 200%;
        }

        .output-table [data-order-fill] {
            width: 21px;
        }

        .output-table [data-lang] {
            display: inline-block;
            vertical-align: top;
            padding-right: 10px;
        }
    </style>



## Example of data from https://rezervace-knezska.c-budejovice.cz/json.php?method=mon_dej_cinnost
 {
    "return": [
        {
            "cinnostID": 1979,
            "nazev": "Evidence motorov\u00fdch vozidel - osobn\u00ed vy\u0159\u00edzen\u00ed",
            "fronta": 0,
            "pocet_prepazek": 10,
            "doba_cekani_odhad": 9999,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 1
        },
        {
            "cinnostID": 1980,
            "nazev": "Evidence motorov\u00fdch vozidel - podniky, firmy,",
            "fronta": 0,
            "pocet_prepazek": 10,
            "doba_cekani_odhad": 9999,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 2
        },
        {
            "cinnostID": 1981,
            "nazev": "Zmocn\u011bn\u00e1 osoba",
            "fronta": 0,
            "pocet_prepazek": 10,
            "doba_cekani_odhad": 9999,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 3
        },
        {
            "cinnostID": 1982,
            "nazev": "Dovozy, stavby a p\u0159estavby",
            "fronta": 0,
            "pocet_prepazek": 2,
            "doba_cekani_odhad": 9999,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 4
        },
        {
            "cinnostID": 1983,
            "nazev": "V\u00fddej doklad\u016f a RZ",
            "fronta": 0,
            "pocet_prepazek": 2,
            "doba_cekani_odhad": 9999,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 5
        },
        {
            "cinnostID": 1984,
            "nazev": "Speci\u00e1ln\u00ed \u010dinnost",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 6
        }
    ],
    "apiInfo": {
        "remote_addr": "92.62.236.198",
        "currentTime": 1758175158,
        "apiVersion": "10.0.3",
        "execTime": 0.029480934143066406,
        "usageMemory": 2253096
    },
    "warning": "ApiKey neni uvedeno."
}

## example of https://rezervace-jeronymova.c-budejovice.cz/json.php?method=mon_dej_cinnost
{
    "return": [
        {
            "cinnostID": 1929,
            "nazev": "E-pasy (\u017d\u00e1dost o pas, ozn\u00e1men\u00ed ztr\u00e1ty, odcizen\u00ed nebo po\u0161kozen\u00ed)",
            "fronta": 0,
            "pocet_prepazek": 12,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 1
        },
        {
            "cinnostID": 1930,
            "nazev": "(CP - blesk)",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 2
        },
        {
            "cinnostID": 1931,
            "nazev": "V\u00fddej hotov\u00fdch pas\u016f",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 3
        },
        {
            "cinnostID": 1932,
            "nazev": "Prvn\u00ed OP",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 4
        },
        {
            "cinnostID": 1933,
            "nazev": "OP - pod\u00e1n\u00ed \u017e\u00e1dosti; Zad\u00e1n\u00ed, zm\u011bna, odblokov\u00e1n\u00ed k\u00f3d\u016f (BOK, IOK, DOK)",
            "fronta": 1,
            "pocet_prepazek": 11,
            "doba_cekani_odhad": 2,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 4,
            "razeni": 5
        },
        {
            "cinnostID": 1934,
            "nazev": "V\u00fddej OP",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 6
        },
        {
            "cinnostID": 1935,
            "nazev": "OP - ozn\u00e1men\u00ed ztr\u00e1ty, odcizen\u00ed, po\u0161kozen\u00ed, zni\u010den\u00ed, zneu\u017eit\u00ed",
            "fronta": 0,
            "pocet_prepazek": 4,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 7
        },
        {
            "cinnostID": 1936,
            "nazev": "V\u00fdpis z informa\u010dn\u00edho syst\u00e9mu; \u017d\u00e1dost o zprost\u0159edkov\u00e1n\u00ed kontaktu",
            "fronta": 0,
            "pocet_prepazek": 3,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 8
        },
        {
            "cinnostID": 1937,
            "nazev": "\u017d\u00e1dost o \u0158P a mezin\u00e1rodn\u00ed \u0158P; Ozn\u00e1men\u00ed ztr\u00e1ty nebo odcizen\u00ed \u0158P",
            "fronta": 0,
            "pocet_prepazek": 7,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 9
        },
        {
            "cinnostID": 1938,
            "nazev": "(V\u00fdm\u011bna \u0158P, M\u0158P) .",
            "fronta": 0,
            "pocet_prepazek": 3,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 10
        },
        {
            "cinnostID": 1939,
            "nazev": "V\u00fddej \u0158P a DK\u0158",
            "fronta": 0,
            "pocet_prepazek": 5,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 11
        },
        {
            "cinnostID": 1940,
            "nazev": "Digit\u00e1ln\u00ed karta \u0159idi\u010de - p\u0159\u00edjem \u017e\u00e1dost\u00ed (pam\u011b\u0165ov\u00e9 karty)",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 12
        },
        {
            "cinnostID": 1941,
            "nazev": "(DK\u0158 v\u00fddej) .",
            "fronta": 0,
            "pocet_prepazek": 3,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 13
        },
        {
            "cinnostID": 1942,
            "nazev": "V\u00fdpisy - K\u0158, body",
            "fronta": 0,
            "pocet_prepazek": 8,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 14
        },
        {
            "cinnostID": 1943,
            "nazev": "\u0158P - spr\u00e1vn\u00ed \u0159\u00edzen\u00ed",
            "fronta": 0,
            "pocet_prepazek": 2,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 15
        }
    ],
    "apiInfo": {
        "remote_addr": "92.62.236.198",
        "currentTime": 1758176034,
        "apiVersion": "10.0.3",
        "execTime": 0.02746295928955078,
        "usageMemory": 2263496
    },
    "warning": "ApiKey neni uvedeno."
}

## Example data of https://rezervace-radnice.c-budejovice.cz/json.php?method=mon_dej_cinnost
{
    "return": [
        {
            "cinnostID": 1944,
            "nazev": "FO-Pokladna",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 1
        },
        {
            "cinnostID": 1958,
            "nazev": "O",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 2
        },
        {
            "cinnostID": 1945,
            "nazev": "FO-Poplatky - psi, ubytovac\u00ed kapacita, rekrea\u010dn\u00ed pobyt",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 3
        },
        {
            "cinnostID": 1946,
            "nazev": "FO-Poplatek tombola, vstupn\u00e9",
            "fronta": 0,
            "pocet_prepazek": 8,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 4
        },
        {
            "cinnostID": 1947,
            "nazev": "FO-Poplatek za komun\u00e1ln\u00ed odpad",
            "fronta": 0,
            "pocet_prepazek": 17,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 5
        },
        {
            "cinnostID": 1948,
            "nazev": "OSV-KOR Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 11,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 6
        },
        {
            "cinnostID": 1949,
            "nazev": "OSV-WIM Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 7
        },
        {
            "cinnostID": 1950,
            "nazev": "OSV-HOU Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 8
        },
        {
            "cinnostID": 1951,
            "nazev": "OSV-STU Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 9
        },
        {
            "cinnostID": 1952,
            "nazev": "OSV-HAV Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 1,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 10
        },
        {
            "cinnostID": 1953,
            "nazev": "OSV-LOW Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 1,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 11
        },
        {
            "cinnostID": 1954,
            "nazev": "OSV-BEC Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 1,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 12
        },
        {
            "cinnostID": 1955,
            "nazev": "OSV-SVO Hmotn\u00e1 nouze",
            "fronta": 0,
            "pocet_prepazek": 1,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 13
        },
        {
            "cinnostID": 1956,
            "nazev": "OSV-HOU Hmotn\u00e1 nouze - OBCE",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 14
        },
        {
            "cinnostID": 1957,
            "nazev": "MU-Voli\u010dsk\u00fd pr\u016fkaz",
            "fronta": 0,
            "pocet_prepazek": 8,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 15
        },
        {
            "cinnostID": 1959,
            "nazev": "MU-Evidence obyvatel, ohla\u0161ovna pobytu",
            "fronta": 0,
            "pocet_prepazek": 4,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 16
        },
        {
            "cinnostID": 1960,
            "nazev": "O",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 17
        },
        {
            "cinnostID": 1961,
            "nazev": "MU-Rejst\u0159\u00edk trest\u016f, ov\u011b\u0159. podpis\u016f a listin",
            "fronta": 0,
            "pocet_prepazek": 6,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 18
        },
        {
            "cinnostID": 1962,
            "nazev": "O",
            "fronta": 0,
            "pocet_prepazek": 0,
            "doba_cekani_odhad": 9998,
            "m_poradi_aktivni": 0,
            "m_aktivni": 0,
            "doba_cekani": 0,
            "razeni": 19
        },
        {
            "cinnostID": 1963,
            "nazev": "MU-Ru\u0161en\u00ed pobytu",
            "fronta": 0,
            "pocet_prepazek": 4,
            "doba_cekani_odhad": 0,
            "m_poradi_aktivni": 0,
            "m_aktivni": 1,
            "doba_cekani": 0,
            "razeni": 20
        }
    ],
    "apiInfo": {
        "remote_addr": "92.62.236.198",
        "currentTime": 1758175992,
        "apiVersion": "10.0.3",
        "execTime": 0.032508134841918945,
        "usageMemory": 2264688
    },
    "warning": "ApiKey neni uvedeno."
}

## Example data from https://www.szcb.cz/plavecky-stadion
<div class="core-row i-theme-dark item-panel" data-flex="column">
                        <div class="panel-percentage color-green">18 %</div>
                        <div class="panel-label">Obsazenost</div>
                        <div class="panel-ratio">60 / 340</div>

            </div>

